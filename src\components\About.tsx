import { Card, CardContent } from './ui/card'

const About = () => {
  const stats = [
    { number: "4", label: "<PERSON>ăm học tập", icon: "🎓" },
    { number: "10+", label: "Dự án cá nhân", icon: "💻" },
    { number: "5+", label: "<PERSON>ông nghệ thành thạo", icon: "🚀" },
    { number: "100%", label: "Đam mê học hỏi", icon: "❤️" }
  ]

  const highlights = [
    {
      title: "Học tập không ngừng",
      description: "<PERSON><PERSON><PERSON> cập nhật những công nghệ mới nhất trong lĩnh vực web development",
      icon: "📚"
    },
    {
      title: "Tư duy logic",
      description: "<PERSON>h<PERSON> năng phân tích và giải quyết vấn đề một cách có hệ thống",
      icon: "🧠"
    },
    {
      title: "<PERSON>à<PERSON> việc nhóm",
      description: "<PERSON><PERSON> nghiệm làm việc nhóm qua các dự án học tập và thực tập",
      icon: "👥"
    },
    {
      title: "<PERSON><PERSON><PERSON> tạo",
      description: "<PERSON><PERSON> mê tạo ra những sản phẩm có giá trị và trải nghiệm người dùng tốt",
      icon: "✨"
    }
  ]

  return (
    <section id="about" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Về <span className="text-blue-600">tôi</span>
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Tìm hiểu thêm về hành trình học tập và đam mê của tôi trong lĩnh vực phát triển web
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left Column - Image and Stats */}
          <div>
            {/* Profile Image */}
            <div className="mb-8">
              <div className="w-80 h-80 mx-auto lg:mx-0 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 p-1">
                <div className="w-full h-full rounded-2xl bg-white flex items-center justify-center">
                  <div className="w-72 h-72 rounded-2xl bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center text-white text-8xl font-bold">
                    👨‍💻
                  </div>
                </div>
              </div>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 gap-4">
              {stats.map((stat, index) => (
                <Card key={index} className="text-center p-4 hover:shadow-lg transition-shadow duration-300">
                  <CardContent className="p-4">
                    <div className="text-2xl mb-2">{stat.icon}</div>
                    <div className="text-2xl font-bold text-blue-600 mb-1">{stat.number}</div>
                    <div className="text-sm text-gray-600">{stat.label}</div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>

          {/* Right Column - Content */}
          <div>
            <div className="mb-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-4">
                Sinh viên Công nghệ thông tin đầy nhiệt huyết
              </h3>
              <div className="space-y-4 text-gray-600">
                <p>
                  Xin chào! Tôi là một sinh viên năm 4 chuyên ngành Công nghệ thông tin tại 
                  [Tên trường đại học]. Với niềm đam mê mãnh liệt dành cho lập trình và 
                  phát triển web, tôi đã dành 4 năm qua để học hỏi và rèn luyện kỹ năng.
                </p>
                <p>
                  Tôi đặc biệt yêu thích việc tạo ra những ứng dụng web hiện đại, thân thiện 
                  với người dùng và có hiệu suất cao. Từ việc thiết kế giao diện đẹp mắt đến 
                  xây dựng logic backend phức tạp, tôi luôn tìm kiếm cơ hội để học hỏi và 
                  áp dụng những công nghệ mới nhất.
                </p>
                <p>
                  Mục tiêu của tôi là trở thành một Full-stack Developer chuyên nghiệp, 
                  có thể đóng góp tích cực vào sự phát triển của công ty và tạo ra những 
                  sản phẩm có giá trị thực sự cho người dùng.
                </p>
              </div>
            </div>

            {/* Highlights */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {highlights.map((highlight, index) => (
                <div key={index} className="flex items-start space-x-3 p-4 rounded-lg hover:bg-gray-50 transition-colors duration-200">
                  <div className="text-2xl">{highlight.icon}</div>
                  <div>
                    <h4 className="font-semibold text-gray-900 mb-1">{highlight.title}</h4>
                    <p className="text-sm text-gray-600">{highlight.description}</p>
                  </div>
                </div>
              ))}
            </div>

            {/* Download CV Button */}
            <div className="mt-8">
              <a 
                href="/cv.pdf" 
                download
                className="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200 font-medium"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Tải CV của tôi
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default About
