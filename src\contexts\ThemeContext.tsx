import React, { createContext, useContext, useEffect, useState } from 'react'

type Theme = 'light' | 'dark'

interface ThemeContextType {
  theme: Theme
  toggleTheme: () => void
  setTheme: (theme: Theme) => void
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export const useTheme = () => {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}

interface ThemeProviderProps {
  children: React.ReactNode
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>('light')

  // Initialize theme from localStorage or default to light
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme') as Theme
    const initialTheme = savedTheme || 'light'

    setThemeState(initialTheme)
    applyTheme(initialTheme)
  }, [])

  // Function to apply theme to DOM
  const applyTheme = (newTheme: Theme) => {
    const root = document.documentElement

    // Remove existing theme classes
    root.classList.remove('dark', 'light')

    // Add new theme class
    if (newTheme === 'dark') {
      root.classList.add('dark')
    }

    // Save to localStorage
    localStorage.setItem('theme', newTheme)

    console.log('Applied theme:', newTheme, 'HTML classes:', root.className)
  }

  // Apply theme when it changes
  useEffect(() => {
    applyTheme(theme)
  }, [theme])

  const setTheme = (newTheme: Theme) => {
    console.log('setTheme called with:', newTheme)
    setThemeState(newTheme)
  }

  const toggleTheme = () => {
    console.log('toggleTheme called, current theme:', theme)
    const newTheme = theme === 'light' ? 'dark' : 'light'
    console.log('Switching to:', newTheme)
    setThemeState(newTheme)
  }

  const value = {
    theme,
    toggleTheme,
    setTheme
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}
