import { useState } from 'react'
import { Button } from './ui/button'

interface DownloadCVProps {
  variant?: 'default' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg'
  className?: string
  showIcon?: boolean
}

const DownloadCV = ({ 
  variant = 'default', 
  size = 'default', 
  className = '',
  showIcon = true 
}: DownloadCVProps) => {
  const [isDownloading, setIsDownloading] = useState(false)

  const handleDownload = async () => {
    setIsDownloading(true)
    
    try {
      // Create a link element and trigger download
      const link = document.createElement('a')
      link.href = '/cv-tran-minh-quan.pdf'
      link.download = 'CV-Tran-Minh-Quan.pdf'
      link.target = '_blank'
      
      // Append to body, click, and remove
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      
      // Add a small delay for better UX
      await new Promise(resolve => setTimeout(resolve, 1000))
    } catch (error) {
      console.error('Error downloading CV:', error)
    } finally {
      setIsDownloading(false)
    }
  }

  return (
    <Button
      onClick={handleDownload}
      disabled={isDownloading}
      variant={variant}
      size={size}
      className={`group relative overflow-hidden ${className}`}
    >
      <div className="flex items-center space-x-2">
        {showIcon && (
          <svg 
            className={`transition-transform duration-300 ${
              isDownloading ? 'animate-bounce' : 'group-hover:translate-y-[-2px]'
            }`}
            width="20" 
            height="20" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            {isDownloading ? (
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" 
              />
            ) : (
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" 
              />
            )}
          </svg>
        )}
        <span className="font-medium">
          {isDownloading ? 'Đang tải...' : 'Tải CV'}
        </span>
      </div>
      
      {/* Hover effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -translate-x-full group-hover:translate-x-full transition-transform duration-700 ease-in-out" />
    </Button>
  )
}

export default DownloadCV
