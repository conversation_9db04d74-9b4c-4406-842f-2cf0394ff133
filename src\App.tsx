import { useEffect } from 'react'
import { ThemeProvider } from './contexts/ThemeContext'
import Header from './components/Header'
import Hero from './components/Hero'
import About from './components/About'
import Skills from './components/Skills'
import Projects from './components/Projects'
import Contact from './components/Contact'
import Footer from './components/Footer'

function AppContent() {
  useEffect(() => {
    // Remove preload class after initial render to enable transitions
    document.body.classList.remove('preload')
  }, [])

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 theme-transition">
      <Header />
      <main>
        <Hero />
        <About />
        <Skills />
        <Projects />
        <Contact />
      </main>
      <Footer />
    </div>
  )
}

function App() {
  useEffect(() => {
    // Add preload class to prevent transitions on initial load
    document.body.classList.add('preload')
  }, [])

  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  )
}

export default App
