import { useTheme } from '../contexts/ThemeContext'

const ThemeToggle = () => {
  const { theme, toggleTheme, isTransitioning } = useTheme()

  return (
    <button
      onClick={toggleTheme}
      disabled={isTransitioning}
      className={`
        relative w-14 h-7 rounded-full p-1 transition-all duration-300 ease-in-out
        ${theme === 'dark' 
          ? 'bg-blue-600 shadow-lg shadow-blue-500/25' 
          : 'bg-gray-300 shadow-lg shadow-gray-400/25'
        }
        hover:scale-105 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
        dark:focus:ring-offset-gray-800
        ${isTransitioning ? 'cursor-wait' : 'cursor-pointer'}
      `}
      aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} mode`}
    >
      {/* Toggle Circle */}
      <div
        className={`
          relative w-5 h-5 rounded-full transition-all duration-300 ease-in-out transform
          ${theme === 'dark' 
            ? 'translate-x-7 bg-white shadow-lg' 
            : 'translate-x-0 bg-white shadow-md'
          }
          ${isTransitioning ? 'scale-90' : 'scale-100'}
        `}
      >
        {/* Sun Icon */}
        <div
          className={`
            absolute inset-0 flex items-center justify-center transition-all duration-300
            ${theme === 'dark' ? 'opacity-0 rotate-180' : 'opacity-100 rotate-0'}
          `}
        >
          <svg className="w-3 h-3 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
            <path
              fillRule="evenodd"
              d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z"
              clipRule="evenodd"
            />
          </svg>
        </div>

        {/* Moon Icon */}
        <div
          className={`
            absolute inset-0 flex items-center justify-center transition-all duration-300
            ${theme === 'dark' ? 'opacity-100 rotate-0' : 'opacity-0 -rotate-180'}
          `}
        >
          <svg className="w-3 h-3 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
            <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
          </svg>
        </div>
      </div>

      {/* Background Animation */}
      <div
        className={`
          absolute inset-0 rounded-full transition-all duration-500 ease-in-out
          ${theme === 'dark' 
            ? 'bg-gradient-to-r from-blue-600 to-purple-600' 
            : 'bg-gradient-to-r from-yellow-400 to-orange-400'
          }
          opacity-0 hover:opacity-20
        `}
      />

      {/* Ripple Effect */}
      {isTransitioning && (
        <div className="absolute inset-0 rounded-full bg-white opacity-30 animate-ping" />
      )}
    </button>
  )
}

export default ThemeToggle
