import { useTheme } from '../contexts/ThemeContext'

const ThemeDebug = () => {
  const { theme, toggleTheme, setTheme } = useTheme()

  const forceLight = () => {
    console.log('Force light theme')
    setTheme('light')
  }

  const forceDark = () => {
    console.log('Force dark theme')
    setTheme('dark')
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 p-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-w-xs">
      <div className="text-sm space-y-2">
        <p className="text-gray-900 dark:text-white">Current theme: <strong>{theme}</strong></p>
        <p className="text-gray-600 dark:text-gray-300 text-xs">HTML classes: {document.documentElement.className}</p>
        <div className="flex gap-2">
          <button
            onClick={toggleTheme}
            className="px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600"
          >
            Toggle
          </button>
          <button
            onClick={forceLight}
            className="px-3 py-1 bg-yellow-500 text-white rounded text-xs hover:bg-yellow-600"
          >
            Light
          </button>
          <button
            onClick={forceDark}
            className="px-3 py-1 bg-gray-700 text-white rounded text-xs hover:bg-gray-800"
          >
            Dark
          </button>
        </div>
      </div>
    </div>
  )
}

export default ThemeDebug
