import { useState, useEffect } from 'react'

const SimpleThemeTest = () => {
  const [isDark, setIsDark] = useState(false)

  const toggleTheme = () => {
    const newTheme = !isDark
    setIsDark(newTheme)
    
    const root = document.documentElement
    if (newTheme) {
      root.classList.add('dark')
      localStorage.setItem('theme', 'dark')
    } else {
      root.classList.remove('dark')
      localStorage.setItem('theme', 'light')
    }
    
    console.log('Simple toggle - isDark:', newTheme, 'HTML classes:', root.className)
  }

  useEffect(() => {
    // Initialize from localStorage
    const savedTheme = localStorage.getItem('theme')
    const initialDark = savedTheme === 'dark'
    setIsDark(initialDark)
    
    const root = document.documentElement
    if (initialDark) {
      root.classList.add('dark')
    } else {
      root.classList.remove('dark')
    }
  }, [])

  return (
    <div className="fixed top-20 right-4 z-50 p-4 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg">
      <div className="text-sm space-y-2">
        <h3 className="font-bold text-gray-900 dark:text-white">Simple Theme Test</h3>
        <p className="text-gray-600 dark:text-gray-300">Current: {isDark ? 'Dark' : 'Light'}</p>
        <button 
          onClick={toggleTheme}
          className="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded transition-colors"
        >
          Toggle to {isDark ? 'Light' : 'Dark'}
        </button>
        <div className="mt-2 p-2 bg-gray-100 dark:bg-gray-700 rounded">
          <p className="text-xs text-gray-600 dark:text-gray-300">
            This box should change color when toggling
          </p>
        </div>
      </div>
    </div>
  )
}

export default SimpleThemeTest
